'use client'

import {
  Group,
  Avatar,
  Text,
  UnstyledButton,
  Menu,
  rem,
  Box,
} from '@mantine/core'
import {
  IconChevronUp,
  IconSettings,
  IconUser,
  IconLogout,
  IconBell,
} from '@tabler/icons-react'

interface NavUserProps {
  user: {
    name: string
    email: string
    avatar: string
  }
}

export function NavUser({ user }: NavUserProps) {
  return (
    <Menu shadow="md" width={200} position="top-start">
      <Menu.Target>
        <UnstyledButton
          style={{
            display: 'block',
            width: '100%',
            padding: rem(8),
            borderRadius: 'var(--mantine-radius-sm)',
            color: 'var(--mantine-color-text)',
            border: '1px solid var(--mantine-color-gray-3)',
          }}
        >
          <Group>
            <Avatar
              src={user.avatar}
              size={32}
              radius="sm"
              style={{
                backgroundColor: 'var(--mantine-color-gray-1)',
                color: 'var(--mantine-color-text)',
              }}
            >
              {user.name.slice(0, 2).toUpperCase()}
            </Avatar>
            <Box style={{ flex: 1 }}>
              <Text size="sm" fw={500}>
                {user.name}
              </Text>
              <Text size="xs" c="dimmed">
                {user.email}
              </Text>
            </Box>
            <IconChevronUp size={16} />
          </Group>
        </UnstyledButton>
      </Menu.Target>

      <Menu.Dropdown>
        <Menu.Label>Account</Menu.Label>
        <Menu.Item leftSection={<IconUser size={14} />}>
          Profile
        </Menu.Item>
        <Menu.Item leftSection={<IconSettings size={14} />}>
          Settings
        </Menu.Item>
        <Menu.Item leftSection={<IconBell size={14} />}>
          Notifications
        </Menu.Item>
        
        <Menu.Divider />
        
        <Menu.Item
          leftSection={<IconLogout size={14} />}
          color="red"
        >
          Sign out
        </Menu.Item>
      </Menu.Dropdown>
    </Menu>
  )
}
