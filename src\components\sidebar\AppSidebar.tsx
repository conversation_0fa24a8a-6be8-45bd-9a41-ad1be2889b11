'use client'

import { Avatar, Box, Group, rem, <PERSON>rollArea, Stack, Text, UnstyledButton } from '@mantine/core'
import { IconBuilding, IconChevronDown } from '@tabler/icons-react'

import { NavMain } from './NavMain'
import { NavProjects } from './NavProjects'
import { NavUser } from './NavUser'

const data = {
  user: {
    name: 'shadcn',
    email: '<EMAIL>',
    avatar: '/avatars/shadcn.jpg',
  },
  teams: [
    {
      name: 'Acme Inc',
      logo: IconBuilding,
      plan: 'Enterprise',
    },
  ],
  navMain: [
    {
      title: 'Platform',
      url: '#',
      icon: IconBuilding,
      isActive: true,
      items: [
        {
          title: 'History',
          url: '#',
        },
        {
          title: 'Starred',
          url: '#',
        },
        {
          title: 'Settings',
          url: '#',
        },
      ],
    },
    {
      title: 'Models',
      url: '#',
      icon: IconBuilding,
      items: [
        {
          title: 'Genesis',
          url: '#',
        },
        {
          title: 'Explorer',
          url: '#',
        },
        {
          title: 'Quantum',
          url: '#',
        },
      ],
    },
    {
      title: 'Documentation',
      url: '#',
      icon: IconBuilding,
      items: [
        {
          title: 'Introduction',
          url: '#',
        },
        {
          title: 'Get Started',
          url: '#',
        },
        {
          title: 'Tutorials',
          url: '#',
        },
        {
          title: 'Changelog',
          url: '#',
        },
      ],
    },
    {
      title: 'Settings',
      url: '#',
      icon: IconBuilding,
      items: [
        {
          title: 'General',
          url: '#',
        },
        {
          title: 'Team',
          url: '#',
        },
        {
          title: 'Billing',
          url: '#',
        },
        {
          title: 'Limits',
          url: '#',
        },
      ],
    },
  ],
  projects: [
    {
      name: 'Design Engineering',
      url: '#',
      icon: IconBuilding,
    },
    {
      name: 'Sales & Marketing',
      url: '#',
      icon: IconBuilding,
    },
    {
      name: 'Travel',
      url: '#',
      icon: IconBuilding,
    },
  ],
}

export function AppSidebar() {
  return (
    <Stack h="100%" gap={0}>
      {/* Team Switcher */}
      <Box p="md">
        <UnstyledButton
          style={{
            display: 'block',
            width: '100%',
            padding: rem(8),
            borderRadius: 'var(--mantine-radius-sm)',
            color: 'var(--mantine-color-text)',
          }}
        >
          <Group>
            <Avatar size={24} radius="sm">
              <IconBuilding size={16} />
            </Avatar>
            <Box style={{ flex: 1 }}>
              <Text size="sm" fw={500}>
                {data.teams[0].name}
              </Text>
              <Text size="xs" c="dimmed">
                {data.teams[0].plan}
              </Text>
            </Box>
            <IconChevronDown size={16} />
          </Group>
        </UnstyledButton>
      </Box>

      {/* Scrollable content */}
      <ScrollArea style={{ flex: 1 }} p="md" pt={0}>
        <Stack gap="lg">
          <NavMain items={data.navMain} />
          <NavProjects projects={data.projects} />
        </Stack>
      </ScrollArea>

      {/* User section at bottom */}
      <Box p="md" pt={0}>
        <NavUser user={data.user} />
      </Box>
    </Stack>
  )
}
