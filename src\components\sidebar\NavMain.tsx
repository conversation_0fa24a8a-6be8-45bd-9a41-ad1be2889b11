'use client'

import { useState } from 'react'
import {
  NavLink,
  Stack,
  Text,
  Collapse,
  Group,
  UnstyledButton,
  rem,
} from '@mantine/core'
import { IconChevronRight } from '@tabler/icons-react'

interface NavMainProps {
  items: {
    title: string
    url: string
    icon?: React.ComponentType<any>
    isActive?: boolean
    items?: {
      title: string
      url: string
      isActive?: boolean
    }[]
  }[]
}

export function NavMain({ items }: NavMainProps) {
  const [openItems, setOpenItems] = useState<Record<string, boolean>>({
    Platform: true, // Platform starts open by default
  })

  const toggleItem = (title: string) => {
    setOpenItems(prev => ({
      ...prev,
      [title]: !prev[title],
    }))
  }

  return (
    <Stack gap="xs">
      <Text size="xs" fw={600} c="dimmed" tt="uppercase" px="sm">
        Platform
      </Text>
      {items.map((item) => {
        const Icon = item.icon
        const isOpen = openItems[item.title]
        const hasChildren = item.items && item.items.length > 0

        return (
          <div key={item.title}>
            {hasChildren ? (
              <>
                <UnstyledButton
                  onClick={() => toggleItem(item.title)}
                  style={{
                    display: 'block',
                    width: '100%',
                    padding: `${rem(8)} ${rem(12)}`,
                    borderRadius: 'var(--mantine-radius-sm)',
                    color: 'var(--mantine-color-text)',
                    backgroundColor: item.isActive ? 'var(--mantine-color-gray-1)' : 'transparent',
                  }}
                  data-active={item.isActive || undefined}
                >
                  <Group justify="space-between">
                    <Group gap="sm">
                      {Icon && <Icon size={16} />}
                      <Text size="sm" fw={500}>
                        {item.title}
                      </Text>
                    </Group>
                    <IconChevronRight
                      size={14}
                      style={{
                        transform: isOpen ? 'rotate(90deg)' : 'rotate(0deg)',
                        transition: 'transform 200ms ease',
                      }}
                    />
                  </Group>
                </UnstyledButton>
                <Collapse in={isOpen}>
                  <Stack gap={2} pl="lg" pt="xs">
                    {item.items?.map((subItem) => (
                      <NavLink
                        key={subItem.title}
                        href={subItem.url}
                        label={subItem.title}
                        active={subItem.isActive}
                        variant="subtle"
                        style={{
                          borderRadius: 'var(--mantine-radius-sm)',
                          fontSize: rem(14),
                        }}
                      />
                    ))}
                  </Stack>
                </Collapse>
              </>
            ) : (
              <NavLink
                href={item.url}
                label={item.title}
                leftSection={Icon && <Icon size={16} />}
                active={item.isActive}
                variant="subtle"
                style={{
                  borderRadius: 'var(--mantine-radius-sm)',
                }}
              />
            )}
          </div>
        )
      })}
    </Stack>
  )
}
