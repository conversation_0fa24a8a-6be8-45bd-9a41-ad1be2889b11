'use client'

import { Anchor, AppShell, Box, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Divider, Group, Text } from '@mantine/core'
import { useDisclosure } from '@mantine/hooks'

import { AppSidebar } from './AppSidebar'

export function SidebarLayout() {
  const [opened, { toggle }] = useDisclosure()

  return (
    <AppShell
      navbar={{
        width: '250',
        breakpoint: 'sm',
        collapsed: { mobile: !opened },
      }}
    >
      <AppShell.Navbar>
        <AppSidebar />
      </AppShell.Navbar>

      <AppShell.Main>
        <Box mb="md">
          <Group h={60} px="md" style={{ borderBottom: '1px solid var(--mantine-color-gray-3)' }}>
            <Burger opened={opened} onClick={toggle} hiddenFrom="sm" size="sm" />
            <Divider orientation="vertical" />
            <Breadcrumbs>
              <Anchor href="#" size="sm" c="dimmed">
                Building Your Application
              </Anchor>
              <Text size="sm">Data Fetching</Text>
            </Breadcrumbs>
          </Group>
        </Box>

        <Box p="md">
          {/* Main content area */}
          <div
            style={{
              display: 'grid',
              gap: '1rem',
              gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
              marginBottom: '1rem',
            }}
          >
            <Box h={200} bg="gray.1" style={{ borderRadius: 'var(--mantine-radius-md)' }} />
            <Box h={200} bg="gray.1" style={{ borderRadius: 'var(--mantine-radius-md)' }} />
            <Box h={200} bg="gray.1" style={{ borderRadius: 'var(--mantine-radius-md)' }} />
          </div>
          <Box h={400} bg="gray.1" style={{ borderRadius: 'var(--mantine-radius-md)' }} />
        </Box>
      </AppShell.Main>
    </AppShell>
  )
}
