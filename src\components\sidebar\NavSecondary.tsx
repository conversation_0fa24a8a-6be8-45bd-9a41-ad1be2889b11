'use client'

import { Stack, NavLink } from '@mantine/core'

interface NavSecondaryProps {
  items: {
    title: string
    url: string
    icon?: React.ComponentType<any>
  }[]
  className?: string
}

export function NavSecondary({ items, className }: NavSecondaryProps) {
  return (
    <Stack gap="xs" className={className}>
      {items.map((item) => {
        const Icon = item.icon

        return (
          <NavLink
            key={item.title}
            href={item.url}
            label={item.title}
            leftSection={Icon && <Icon size={16} />}
            variant="subtle"
            style={{
              borderRadius: 'var(--mantine-radius-sm)',
            }}
          />
        )
      })}
    </Stack>
  )
}
