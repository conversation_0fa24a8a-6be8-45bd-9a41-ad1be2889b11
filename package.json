{"name": "nextjs-template", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@mantine/core": "^8.0.1", "@mantine/hooks": "^8.0.1", "@tabler/icons-react": "^3.33.0", "@tanstack/react-form": "^1.11.1", "@tanstack/react-table": "^8.21.3", "convex": "^1.24.1", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "zod": "^3.24.4", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@ianvs/prettier-plugin-sort-imports": "^4.4.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "babel-plugin-react-compiler": "19.1.0-rc.2", "eslint": "^9", "eslint-config-mantine": "^4.0.3", "eslint-config-next": "15.3.2", "postcss": "^8.5.3", "postcss-preset-mantine": "^1.17.0", "postcss-simple-vars": "^7.0.1", "prettier": "^3.5.3", "typescript": "^5"}, "packageManager": "pnpm@10.11.0"}