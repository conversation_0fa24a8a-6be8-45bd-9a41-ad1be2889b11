'use client'

import {
  Stack,
  Text,
  NavLink,
  Group,
  ActionIcon,
  Menu,
  rem,
} from '@mantine/core'
import { IconDots, IconPlus } from '@tabler/icons-react'

interface NavProjectsProps {
  projects: {
    name: string
    url: string
    icon?: React.ComponentType<any>
  }[]
}

export function NavProjects({ projects }: NavProjectsProps) {
  return (
    <Stack gap="xs">
      <Group justify="space-between" px="sm">
        <Text size="xs" fw={600} c="dimmed" tt="uppercase">
          Projects
        </Text>
        <ActionIcon variant="subtle" size="sm" color="gray">
          <IconPlus size={12} />
        </ActionIcon>
      </Group>
      
      {projects.map((project) => {
        const Icon = project.icon

        return (
          <Group key={project.name} gap={0} justify="space-between">
            <NavLink
              href={project.url}
              label={project.name}
              leftSection={Icon && <Icon size={16} />}
              variant="subtle"
              style={{
                borderRadius: 'var(--mantine-radius-sm)',
                flex: 1,
              }}
            />
            <Menu shadow="md" width={200} position="bottom-end">
              <Menu.Target>
                <ActionIcon
                  variant="subtle"
                  size="sm"
                  color="gray"
                  style={{ marginRight: rem(8) }}
                >
                  <IconDots size={12} />
                </ActionIcon>
              </Menu.Target>

              <Menu.Dropdown>
                <Menu.Item>View Project</Menu.Item>
                <Menu.Item>Share Project</Menu.Item>
                <Menu.Item>Rename Project</Menu.Item>
                <Menu.Divider />
                <Menu.Item color="red">Delete Project</Menu.Item>
              </Menu.Dropdown>
            </Menu>
          </Group>
        )
      })}
      
      <NavLink
        label="More"
        variant="subtle"
        style={{
          borderRadius: 'var(--mantine-radius-sm)',
          color: 'var(--mantine-color-dimmed)',
        }}
      />
    </Stack>
  )
}
